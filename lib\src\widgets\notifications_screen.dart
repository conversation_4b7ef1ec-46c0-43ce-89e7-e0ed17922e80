import 'package:droit/src/config/colors.dart';
import 'package:droit/src/config/theme_helper.dart';
import 'package:droit/src/config/app_localizations.dart';
import 'package:droit/src/widgets/locale_provider.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final List<Map<String, dynamic>> notifications = [
    {
      'type': 'rejection',
      'requestId': 'DEM-2023-001',
      'date': DateTime.now().subtract(const Duration(days: 1)),
      'message': 'Votre demande a été refusée',
      'messageAr': 'تم رفض طلبك',
      'details': 'Documents incomplets',
      'detailsAr': 'وثائق غير مكتملة',
    },
    {
      'type': 'approval',
      'requestId': 'DEM-2023-002',
      'date': DateTime.now().subtract(const Duration(days: 2)),
      'message': 'Votre demande a été approuvée',
      'messageAr': 'تمت الموافقة على طلبك',
      'details': 'Vous pouvez maintenant procéder au paiement',
      'detailsAr': 'يمكنك الآن المتابعة للدفع',
    },
    {
      'type': 'expiration',
      'requestId': 'DEM-2023-003',
      'date': DateTime.now().subtract(const Duration(days: 3)),
      'message': 'Votre permis expire bientôt',
      'messageAr': 'تصريحك على وشك الانتهاء',
      'details': 'Expiration dans 30 jours',
      'detailsAr': 'ينتهي في غضون 30 يومًا',
      'expiryDate': DateTime.now().add(const Duration(days: 30)),
    },
  ];

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;
    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      appBar: AppBar(
        title: Text(AppLocalizations.tr(context, 'notifications')),
        backgroundColor: colors.backgroundSecondary,
        foregroundColor: colors.textPrimary,
        elevation: 0,
        iconTheme: IconThemeData(color: colors.textPrimary),
      ),
      body: notifications.isEmpty
          ? Center(
              child: Text(
                AppLocalizations.tr(context, 'no_notifications'),
                style: ThemeHelper.getSectionTitleStyle(context),
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16.0),
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                return _buildNotificationCard(notifications[index], isArabic, context);
              },
            ),
    );
  }

  Widget _buildNotificationCard(Map<String, dynamic> notification, bool isArabic, BuildContext context) {
    final colors = ThemeHelper.getColors(context);
    IconData iconData;
    Color iconColor;

    switch (notification['type']) {
      case 'rejection':
        iconData = Icons.cancel_outlined;
        iconColor = AppColors.error;
        break;
      case 'approval':
        iconData = Icons.check_circle_outline;
        iconColor = AppColors.success;
        break;
      case 'expiration':
        iconData = Icons.access_time;
        iconColor = AppColors.warning;
        break;
      default:
        iconData = Icons.notifications_outlined;
        iconColor = AppColors.info;
    }

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16.0),
      color: colors.card,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  iconData,
                  color: iconColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? notification['messageAr'] : notification['message'],
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: iconColor,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${isArabic ? 'رقم الطلب: ' : 'Demande N°: '}${notification['requestId']}',
                        style: TextStyle(
                          fontSize: 14,
                          color: colors.textSecondary,
                          fontFamily: 'Cairo',
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              isArabic ? notification['detailsAr'] : notification['details'],
              style: TextStyle(
                fontSize: 14,
                color: colors.textPrimary,
                fontFamily: 'Cairo',
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _formatDate(notification['date'], isArabic),
              style: TextStyle(
                fontSize: 12,
                color: colors.textSecondary,
                fontFamily: 'Cairo',
              ),
            ),
            if (notification['type'] == 'expiration' && notification['expiryDate'] != null) ...[
              const SizedBox(height: 12),
              _buildExpirationWarning(notification['expiryDate'] as DateTime, isArabic),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildExpirationWarning(DateTime expiryDate, bool isArabic) {
    final daysRemaining = expiryDate.difference(DateTime.now()).inDays;
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.warning.withOpacity(0.15),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.warning.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: AppColors.warning,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              isArabic
                  ? 'متبقي $daysRemaining يوم على انتهاء التصريح'
                  : 'Il reste $daysRemaining jours avant expiration',
              style: TextStyle(
                color: AppColors.warning,
                fontWeight: FontWeight.w600,
                fontSize: 14,
                fontFamily: 'Cairo',
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date, bool isArabic) {
    final formatter = DateFormat(isArabic ? 'dd/MM/yyyy HH:mm' : 'dd/MM/yyyy HH:mm');
    return isArabic
        ? 'تاريخ الإشعار: ${formatter.format(date)}'
        : 'Date de notification: ${formatter.format(date)}';
  }
}
