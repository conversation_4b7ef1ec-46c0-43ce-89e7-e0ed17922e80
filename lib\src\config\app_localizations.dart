import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/locale_provider.dart';

class AppLocalizations {
  static String getTranslation(BuildContext context, String key) {
    final locale = Localizations.localeOf(context);
    return _translations[locale.languageCode]?[key] ?? _translations['en']![key]!;
  }

  static String getTranslationByLanguage(String languageCode, String key) {
    return _translations[languageCode]?[key] ?? _translations['en']![key]!;
  }

  // Helper method to get translation using LocaleProvider
  static String tr(BuildContext context, String key) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    return _translations[localeProvider.currentLanguage]?[key] ?? _translations['en']![key]!;
  }

  static const Map<String, Map<String, String>> _translations = {
    'ar': {
      // App Title
      'app_title': 'منصة بناء',
      'app_subtitle': 'اهلا بك في منصة بناء',
      
      // Settings Screen
      'settings': 'الإعدادات',
      'profile': 'الملف الشخصي',
      'edit_profile': 'تعديل الملف الشخصي',
      'language': 'اللغة',
      'choose_language': 'اختر اللغة',
      'appearance': 'المظهر',
      'light': 'فاتح',
      'dark': 'داكن',
      'logout': 'تسجيل الخروج',
      
      // Login Screen
      'login': 'تسجيل الدخول',
      'login_subtitle': 'أدخل بريدك الإلكتروني وكلمة المرور للوصول إلى حسابك',
      'email': 'البريد الإلكتروني',
      'password': 'كلمة المرور',
      'forgot_password': 'نسيت كلمة المرور؟',
      'no_account': 'ليس لديك حساب؟',
      'sign_up': 'إنشاء حساب',

      // Sign Up Screen
      'create_account': 'إنشاء حساب جديد',
      'signup_subtitle': 'أدخل بياناتك لإنشاء حساب في منصة بناء',
      'enter_phone': 'يرجى إدخال رقم الهاتف',
      'confirm_password_prompt': 'يرجى تأكيد كلمة المرور',
      'already_have_account': 'لديك حساب بالفعل؟',
      
      // Profile Edit
      'full_name': 'الاسم الكامل',
      'phone': 'رقم الهاتف',
      'new_password': 'كلمة المرور الجديدة',
      'confirm_password': 'تأكيد كلمة المرور',
      'save_changes': 'حفظ التغييرات',
      
      // Validation Messages
      'enter_name': 'الرجاء إدخال الاسم',
      'name_too_short': 'الاسم قصير جداً',
      'enter_email': 'الرجاء إدخال البريد الإلكتروني',
      'enter_password': 'الرجاء إدخال كلمة المرور',
      'invalid_email': 'البريد الإلكتروني غير صالح',
      'invalid_phone': 'رقم الهاتف غير صالح',
      'password_min_length': 'كلمة المرور يجب أن تكون 8 أحرف على الأقل',
      'passwords_not_match': 'كلمات المرور غير متطابقة',
      
      // Success/Error Messages
      'profile_updated_success': 'تم تحديث الملف الشخصي بنجاح',
      'profile_update_error': 'حدث خطأ أثناء تحديث الملف الشخصي',
      
      // Guide Screen
      'user_guide': 'دليل الاستخدام',
      
      // Dashboard
      'welcome_message': 'مرحبًا في منصة بناء لإدارة طلبات وتراخيص البناء. يمكنك متابعة طلباتك وإدارة تراخيصك من هنا.',

      // Navigation
      'home': 'الرئيسية',
      'history': 'التاريخ',

      // Dashboard Actions
      'new_request': 'تقديم طلب',
      'new_request_subtitle': 'قم بتقديم طلب رخصة بناء',
      'submit': 'تقديم طلب',
      'renew_request': 'تجديد الطلب',
      'renew_request_subtitle': 'قم بتجديد طلب رخصة بناء قديم',
      'requests': 'الطلبات',
      'requests_subtitle': 'عرض كل الطلبات التي قمت بتقديمها',
      'view_requests': 'عرض الطلبات',
      'view_guide': 'عرض الدليل',
      'guide_subtitle': 'تعرف على كيفية استخدام منصة بناء',

      // History Screen
      'permits': 'الرخص',
      'my_requests': 'طلباتي',
      'request_number': 'رقم الطلب',
      'request_type': 'نوع الطلب',
      'surface': 'المساحة',
      'status': 'الحالة',
      'fees': 'الرسوم',
      'approved': 'موافق عليه',
      'pending': 'قيد المراجعة',
      'rejected': 'مرفوض',
      'pay': 'دفع',
      'appeal': 'استئناف',
      'building_permit': 'رخصة بناء',

      // Notifications
      'notifications': 'الإشعارات',
      'no_notifications': 'لا توجد إشعارات',
      'permit_expires_soon': 'تصريحك على وشك الانتهاء',
      'expires_in_days': 'ينتهي في غضون {days} يومًا',
      'request_approved': 'تمت الموافقة على طلبك',
      'request_rejected': 'تم رفض طلبك',
      'can_proceed_payment': 'يمكنك الآن المتابعة للدفع',
      'incomplete_documents': 'وثائق غير مكتملة',
      'expires_in_30_days': 'ينتهي في غضون 30 يومًا',

      // Guide Screen
      'laws_and_regulations': 'القوانين واللوائح',
      'urban_planning_law': 'القانون رقم 12.90 المتعلق بالتعمير',
      'subdivision_law': 'القانون رقم 25.90 المتعلق بالتجزئات',
      'law_12_90_content': 'المادة 1: يهدف هذا القانون إلى تحديد وثائق التعمير المختلفة...\n\nالمادة 2: يخطط المخطط التوجيهي للتهيئة العمرانية التنظيم العام...',
      'law_25_90_content': 'المادة 1: تعتبر تجزئة كل تقسيم بالبيع...',

      // Payment & Appeal
      'payment_required': 'مطلوب دفع الرسوم',
      'pay_fees': 'دفع الرسوم',
      'submit_appeal': 'تقديم استئناف',
      'dh': 'درهم',
    },
    
    'fr': {
      // App Title
      'app_title': 'Plateforme Bâtir',
      'app_subtitle': 'Bienvenue sur la plateforme Bâtir',
      
      // Settings Screen
      'settings': 'Paramètres',
      'profile': 'Profil',
      'edit_profile': 'Modifier le profil',
      'language': 'Langue',
      'choose_language': 'Choisir la langue',
      'appearance': 'Apparence',
      'light': 'Clair',
      'dark': 'Sombre',
      'logout': 'Déconnexion',
      
      // Login Screen
      'login': 'Connexion',
      'login_subtitle': 'Entrez votre email et mot de passe pour accéder à votre compte',
      'email': 'Email',
      'password': 'Mot de passe',
      'forgot_password': 'Mot de passe oublié ?',
      'no_account': 'Pas de compte ?',
      'sign_up': 'S\'inscrire',

      // Sign Up Screen
      'create_account': 'Créer un nouveau compte',
      'signup_subtitle': 'Entrez vos informations pour créer un compte',
      'enter_phone': 'Veuillez entrer votre numéro de téléphone',
      'confirm_password_prompt': 'Veuillez confirmer votre mot de passe',
      'already_have_account': 'Déjà un compte ?',
      
      // Profile Edit
      'full_name': 'Nom complet',
      'phone': 'Téléphone',
      'new_password': 'Nouveau mot de passe',
      'confirm_password': 'Confirmer le mot de passe',
      'save_changes': 'Enregistrer',
      
      // Validation Messages
      'enter_name': 'Veuillez entrer votre nom',
      'name_too_short': 'Nom trop court',
      'enter_email': 'Veuillez entrer votre email',
      'enter_password': 'Veuillez entrer votre mot de passe',
      'invalid_email': 'Email invalide',
      'invalid_phone': 'Numéro de téléphone invalide',
      'password_min_length': 'Le mot de passe doit contenir au moins 8 caractères',
      'passwords_not_match': 'Les mots de passe ne correspondent pas',
      
      // Success/Error Messages
      'profile_updated_success': 'Profil mis à jour avec succès',
      'profile_update_error': 'Erreur lors de la mise à jour du profil',
      
      // Guide Screen
      'user_guide': 'Guide d\'utilisation',
      
      // Dashboard
      'welcome_message': 'Bienvenue sur la plateforme Bâtir pour gérer vos demandes et permis de construction. Vous pouvez suivre vos demandes et gérer vos permis ici.',

      // Navigation
      'home': 'Accueil',
      'history': 'Historique',

      // Dashboard Actions
      'new_request': 'Nouvelle demande',
      'new_request_subtitle': 'Soumettez une demande de permis',
      'submit': 'Soumettre',
      'renew_request': 'Renouveler une demande',
      'renew_request_subtitle': 'Renouvelez une demande de permis existante',
      'requests': 'Demandes',
      'requests_subtitle': 'Voir toutes vos demandes soumises',
      'view_requests': 'Voir les demandes',
      'view_guide': 'Voir le guide',
      'guide_subtitle': 'Apprenez à utiliser la plateforme Bâtir',

      // History Screen
      'permits': 'Permis',
      'my_requests': 'Mes demandes',
      'request_number': 'Demande N°',
      'request_type': 'Type',
      'surface': 'Surface',
      'status': 'Statut',
      'fees': 'Frais',
      'approved': 'Approuvé',
      'pending': 'En attente',
      'rejected': 'Rejeté',
      'pay': 'Payer',
      'appeal': 'Recours',
      'building_permit': 'Permis de construire',

      // Notifications
      'notifications': 'Notifications',
      'no_notifications': 'Aucune notification',
      'permit_expires_soon': 'Votre permis expire bientôt',
      'expires_in_days': 'Expiration dans {days} jours',
      'request_approved': 'Votre demande a été approuvée',
      'request_rejected': 'Votre demande a été refusée',
      'can_proceed_payment': 'Vous pouvez maintenant procéder au paiement',
      'incomplete_documents': 'Documents incomplets',
      'expires_in_30_days': 'Expiration dans 30 jours',

      // Guide Screen
      'laws_and_regulations': 'Lois et règlements',
      'urban_planning_law': 'Loi n° 12.90 relative à l\'urbanisme',
      'subdivision_law': 'Loi n° 25.90 relative aux lotissements',
      'law_12_90_content': 'Article 1: Cette loi vise à définir les différents documents d\'urbanisme...\n\nArticle 2: Le schéma directeur d\'aménagement urbain planifie l\'organisation générale...',
      'law_25_90_content': 'Article 1: Est considéré comme lotissement toute division par vente...',

      // Payment & Appeal
      'payment_required': 'Paiement requis',
      'pay_fees': 'Payer les frais',
      'submit_appeal': 'Soumettre un recours',
      'dh': 'DH',
    },
    
    'en': {
      // App Title
      'app_title': 'Build Platform',
      'app_subtitle': 'Welcome to the Build Platform',
      
      // Settings Screen
      'settings': 'Settings',
      'profile': 'Profile',
      'edit_profile': 'Edit Profile',
      'language': 'Language',
      'choose_language': 'Choose Language',
      'appearance': 'Appearance',
      'light': 'Light',
      'dark': 'Dark',
      'logout': 'Logout',
      
      // Login Screen
      'login': 'Login',
      'login_subtitle': 'Enter your email and password to access your account',
      'email': 'Email',
      'password': 'Password',
      'forgot_password': 'Forgot Password?',
      'no_account': 'No account?',
      'sign_up': 'Sign Up',

      // Sign Up Screen
      'create_account': 'Create New Account',
      'signup_subtitle': 'Enter your information to create an account',
      'enter_phone': 'Please enter your phone number',
      'confirm_password_prompt': 'Please confirm your password',
      'already_have_account': 'Already have an account?',
      
      // Profile Edit
      'full_name': 'Full Name',
      'phone': 'Phone',
      'new_password': 'New Password',
      'confirm_password': 'Confirm Password',
      'save_changes': 'Save Changes',
      
      // Validation Messages
      'enter_name': 'Please enter your name',
      'name_too_short': 'Name too short',
      'enter_email': 'Please enter your email',
      'enter_password': 'Please enter your password',
      'invalid_email': 'Invalid email',
      'invalid_phone': 'Invalid phone number',
      'password_min_length': 'Password must be at least 8 characters',
      'passwords_not_match': 'Passwords do not match',
      
      // Success/Error Messages
      'profile_updated_success': 'Profile updated successfully',
      'profile_update_error': 'Error updating profile',
      
      // Guide Screen
      'user_guide': 'User Guide',
      
      // Dashboard
      'welcome_message': 'Welcome to the Build Platform for managing your construction requests and permits. You can track your requests and manage your permits here.',

      // Navigation
      'home': 'Home',
      'history': 'History',

      // Dashboard Actions
      'new_request': 'New Request',
      'new_request_subtitle': 'Submit a building permit request',
      'submit': 'Submit',
      'renew_request': 'Renew Request',
      'renew_request_subtitle': 'Renew an existing permit request',
      'requests': 'Requests',
      'requests_subtitle': 'View all your submitted requests',
      'view_requests': 'View Requests',
      'view_guide': 'View Guide',
      'guide_subtitle': 'Learn how to use the Build Platform',

      // History Screen
      'permits': 'Permits',
      'my_requests': 'My Requests',
      'request_number': 'Request No.',
      'request_type': 'Type',
      'surface': 'Surface',
      'status': 'Status',
      'fees': 'Fees',
      'approved': 'Approved',
      'pending': 'Pending',
      'rejected': 'Rejected',
      'pay': 'Pay',
      'appeal': 'Appeal',
      'building_permit': 'Building Permit',

      // Notifications
      'notifications': 'Notifications',
      'no_notifications': 'No notifications',
      'permit_expires_soon': 'Your permit expires soon',
      'expires_in_days': 'Expires in {days} days',
      'request_approved': 'Your request has been approved',
      'request_rejected': 'Your request has been rejected',
      'can_proceed_payment': 'You can now proceed to payment',
      'incomplete_documents': 'Incomplete documents',
      'expires_in_30_days': 'Expires in 30 days',

      // Guide Screen
      'laws_and_regulations': 'Laws and Regulations',
      'urban_planning_law': 'Law No. 12.90 on Urban Planning',
      'subdivision_law': 'Law No. 25.90 on Subdivisions',
      'law_12_90_content': 'Article 1: This law aims to define the various urban planning documents...\n\nArticle 2: The urban development master plan organizes the general layout...',
      'law_25_90_content': 'Article 1: Any division by sale is considered a subdivision...',

      // Payment & Appeal
      'payment_required': 'Payment Required',
      'pay_fees': 'Pay Fees',
      'submit_appeal': 'Submit Appeal',
      'dh': 'DH',
    },
  };
}
