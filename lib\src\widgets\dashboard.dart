import 'package:droit/src/widgets/guide_screen.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/colors.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import 'base_screen.dart';
import 'locale_provider.dart';
import 'NewRequestScreen.dart';
import 'history_screen.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const BaseScreen(
      currentIndex: 0,
      child: DashboardContent(),
    );
  }
}

class DashboardContent extends StatelessWidget {
  const DashboardContent({super.key});

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;

    return Padding(
      padding: Constants.screenPadding,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: isArabic ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: [
            _buildWelcomeHeader(isArabic, context),
            const SizedBox(height: Constants.extraLargeSpacing),
            _buildMainActionsRow(isArabic, context),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader(bool isArabic, BuildContext context) {
    return Column(
      crossAxisAlignment: isArabic ? CrossAxisAlignment.start : CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.tr(context, 'app_subtitle'),
          style: ThemeHelper.getTitleStyle(context),
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
        const SizedBox(height: Constants.smallSpacing),
        Text(
          AppLocalizations.tr(context, 'welcome_message'),
          style: ThemeHelper.getSubtitleStyle(context),
          textAlign: isArabic ? TextAlign.right : TextAlign.left,
        ),
      ],
    );
  }

  Widget _buildMainActionsRow(bool isArabic, BuildContext context) {
    return Column(
      children: [
        _buildActionCard(
          context: context,
          icon: Icons.add_circle_outline,
          iconColor: AppColors.success,
          title: AppLocalizations.tr(context, 'new_request'),
          subtitle: AppLocalizations.tr(context, 'new_request_subtitle'),
          buttonLabel: AppLocalizations.tr(context, 'submit'),
          onButtonPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const NewRequestScreen()),
            );
          },
        ),
        const SizedBox(height: Constants.mediumSpacing),
        _buildActionCard(
          context: context,
          icon: Icons.autorenew,
          iconColor: AppColors.info,
          title: AppLocalizations.tr(context, 'renew_request'),
          subtitle: AppLocalizations.tr(context, 'renew_request_subtitle'),
          buttonLabel: AppLocalizations.tr(context, 'submit'),
          onButtonPressed: () {}, // À implémenter
        ),
        const SizedBox(height: Constants.mediumSpacing),
        _buildActionCard(
          context: context,
          icon: Icons.list_alt,
          iconColor: AppColors.warning,
          title: AppLocalizations.tr(context, 'requests'),
          subtitle: AppLocalizations.tr(context, 'requests_subtitle'),
          buttonLabel: AppLocalizations.tr(context, 'view_requests'),
          onButtonPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const HistoryScreen()),
            );
          },
        ),
        const SizedBox(height: Constants.mediumSpacing),
        _buildActionCard(
          context: context,
          icon: Icons.help_outline,
          iconColor: AppColors.primaryOrange,
          title: AppLocalizations.tr(context, 'user_guide'),
          subtitle: AppLocalizations.tr(context, 'guide_subtitle'),
          buttonLabel: AppLocalizations.tr(context, 'view_guide'),
          onButtonPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const GuideScreen()),
            );
          },
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String buttonLabel,
    VoidCallback? onButtonPressed,
  }) {
    final colors = ThemeHelper.getColors(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: colors.card,
      child: Container(
        width: double.infinity,
        padding: Constants.cardPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: iconColor, size: 28),
                ),
                const SizedBox(width: Constants.smallSpacing),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: ThemeHelper.getSectionTitleStyle(context).copyWith(
                          fontSize: 16,
                          height: 1.2,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: Constants.smallSpacing),
                      Text(
                        subtitle,
                        style: ThemeHelper.getSubtitleStyle(context).copyWith(
                          fontSize: 14,
                          height: 1.2,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: Constants.mediumSpacing),
            Align(
              alignment: Alignment.centerLeft,
              child: TextButton(
                onPressed: onButtonPressed ?? () {},
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  backgroundColor: AppColors.primaryOrange.withOpacity(0.1),
                ),
                child: Text(
                  buttonLabel,
                  style: TextStyle(
                    color: AppColors.primaryOrange,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

