import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../config/models/constants.dart';
import '../config/theme_helper.dart';
import '../config/app_localizations.dart';
import '../widgets/locale_provider.dart';
import '../widgets/dashboard.dart';
import '../widgets/signup.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  String? _validateEmail(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'enter_email');
    }
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+');
    if (!emailRegex.hasMatch(value)) {
      return AppLocalizations.tr(context, 'invalid_email');
    }
    return null;
  }

  String? _validatePassword(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return AppLocalizations.tr(context, 'enter_password');
    }
    if (value.length < 8) {
      return AppLocalizations.tr(context, 'password_min_length');
    }
    return null;
  }

  void _handleSignIn(BuildContext context) {
    if (_formKey.currentState!.validate()) {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const DashboardScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final localeProvider = Provider.of<LocaleProvider>(context);
    final isArabic = localeProvider.isArabic;

    final colors = ThemeHelper.getColorsWithListener(context);

    return Scaffold(
      backgroundColor: colors.backgroundSecondary,
      body: Padding(
        padding: Constants.screenPadding,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                _buildPlatformHeader(isArabic),
                const SizedBox(height: Constants.extraLargeSpacing),
                _buildLoginCard(isArabic),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPlatformHeader(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          AppLocalizations.tr(context, 'app_title'),
          style: ThemeHelper.getTitleStyle(context),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: Constants.smallSpacing),
        Text(
          AppLocalizations.tr(context, 'app_subtitle'),
          style: ThemeHelper.getSubtitleStyle(context),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLoginCard(bool isArabic) {
    final colors = ThemeHelper.getColors(context);

    return Card(
      elevation: Constants.cardTheme.elevation,
      shape: Constants.cardTheme.shape,
      color: colors.card,
        child: Padding(
          padding: Constants.cardPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildLoginHeader(isArabic),
              const SizedBox(height: Constants.largeSpacing),
              _buildEmailField(isArabic),
              const SizedBox(height: Constants.mediumSpacing),
              _buildPasswordField(isArabic),
              const SizedBox(height: Constants.largeSpacing),
              _buildLoginButton(isArabic),
              const SizedBox(height: Constants.mediumSpacing),
              _buildFooterLinks(isArabic),
            ],
          ),
        ),
    );
  }

  Widget _buildLoginHeader(bool isArabic) => Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            AppLocalizations.tr(context, 'login'),
            style: ThemeHelper.getSectionTitleStyle(context),
            textAlign: isArabic ? TextAlign.right : TextAlign.left,
          ),
          const SizedBox(height: Constants.smallSpacing),
          Text(
            AppLocalizations.tr(context, 'login_subtitle'),
            style: ThemeHelper.getSubtitleStyle(context),
            textAlign: isArabic ? TextAlign.right : TextAlign.left,
          ),
        ],
      );

  Widget _buildEmailField(bool isArabic) => TextFormField(
        controller: _emailController,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validateEmail(value, context),
        keyboardType: TextInputType.emailAddress,
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: '${AppLocalizations.tr(context, 'email')} *',
          hintText: '<EMAIL>',
        ),
      );

  Widget _buildPasswordField(bool isArabic) => TextFormField(
        controller: _passwordController,
        obscureText: true,
        textAlign: isArabic ? TextAlign.right : TextAlign.left,
        validator: (value) => _validatePassword(value, context),
        decoration: ThemeHelper.getInputDecoration(
          context,
          labelText: '${AppLocalizations.tr(context, 'password')} *',
          hintText: '********',
        ),
      );

  Widget _buildLoginButton(bool isArabic) => SizedBox(
        width: double.infinity,
        child: Builder(
          builder: (context) => ElevatedButton(
            onPressed: () => _handleSignIn(context),
            style: ThemeHelper.getPrimaryButtonStyle(context),
            child: Text(
              AppLocalizations.tr(context, 'login'),
              style: ThemeHelper.getButtonTextStyle(context),
            ),
          ),
        ),
      );

  Widget _buildFooterLinks(bool isArabic) => Column(
        children: [
          Align(
            alignment: Alignment.center,
            child: Builder(
              builder: (context) {
                final colors = ThemeHelper.getColors(context);
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      AppLocalizations.tr(context, 'no_account'),
                      style: ThemeHelper.getSubtitleStyle(context),
                    ),
                    TextButton(
                      onPressed: () => Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const SignUpScreen()),
                      ),
                      child: Text(
                        AppLocalizations.tr(context, 'sign_up'),
                        style: ThemeHelper.getSubtitleStyle(context).copyWith(
                          color: colors.textAccent,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
}
